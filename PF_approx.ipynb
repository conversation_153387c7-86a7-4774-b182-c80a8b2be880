import numpy as np
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt

# =============================================================================
# 1. 案例定义 (根据论文 Sec. III.C)
# =============================================================================
# --- 杜芬振荡器动力学 ---
# 论文中没有给出阻尼系数，我们参考通常的杜芬模型，设一个小的阻尼
DAMPING_DELTA = 0.1 

def duffing_drift(x):
  """漂移项 f(x)"""
  x1, x2 = x
  return np.array([x2, x1 - x1**3 - DAMPING_DELTA * x2])

def duffing_control_field(x):
  """控制项 g(x)"""
  return np.array([0.0, 1.0])

def duffing_dynamics(t, x, u_func):
  """完整的杜芬动力学"""
  u = u_func(t)
  return duffing_drift(x) + duffing_control_field(x) * u

# --- 仿真和控制参数 ---
# 控制输入 u(t) = sin(4t)
def control_input(t):
  return np.sin(4 * t)

# 初始条件 (用于生成样本)
mean_x0 = np.array([-0.5, 1.0])
cov_x0 = np.diag([0.05, 0.05])
N_SAMPLES_MC = 1000  # 用于生成"真值"的蒙特卡洛样本数

# 仿真时间
T_FINAL = 5.0
t_eval = np.linspace(0, T_FINAL, 200)

# =============================================================================
# 2. 数据驱动建模: EDMD 计算 L0 和 B1
# =============================================================================

# --- 2.1 定义基函数字典 ---
def create_rbf_dictionary(grid_min, grid_max, num_per_dim, sigma):
  """创建高斯RBF基函数字典"""
  centers_x1 = np.linspace(grid_min, grid_max, num_per_dim)
  centers_x2 = np.linspace(grid_min, grid_max, num_per_dim)
  grid_centers = np.array(np.meshgrid(centers_x1, centers_x2)).T.reshape(-1, 2)
  
  # Psi(x) 是一个返回向量的函数
  def Psi(x):
    diff = grid_centers - x
    sq_dist = np.sum(diff**2, axis=1)
    return np.exp(-sq_dist / (2 * sigma**2))

  return Psi, grid_centers

# 论文中指定的字典参数
GRID_MIN, GRID_MAX = -2.5, 2.5
NUM_PER_DIM = 30
SIGMA_RBF = 0.5 # RBF宽度，可以调整
Psi, centers = create_rbf_dictionary(GRID_MIN, GRID_MAX, NUM_PER_DIM, SIGMA_RBF)
K_DIM = len(centers) # 字典维度: 30*30 = 900

# --- 2.2 生成建模数据 ---
print("正在生成用于EDMD建模的数据...")
DT_EDMD = 0.005 # 论文指定的时间步长
# 论文指定使用 50x50 网格生成初始点
num_grid_points = 50
x1_grid = np.linspace(GRID_MIN, GRID_MAX, num_grid_points)
x2_grid = np.linspace(GRID_MIN, GRID_MAX, num_grid_points)
X_data_grid = np.array(np.meshgrid(x1_grid, x2_grid)).T.reshape(-1, 2)
M_SAMPLES = len(X_data_grid)

# --- 2.3 计算 L0 ---
print("正在计算 L0...")
# 1. 仅在漂移场下演化
Y_data_f = np.array([x + duffing_drift(x) * DT_EDMD for x in X_data_grid])

# 2. 将数据点提升到基函数空间
Psi_X = np.array([Psi(x) for x in X_data_grid]).T  # Shape: (K_DIM, M_SAMPLES)
Psi_Yf = np.array([Psi(y) for y in Y_data_f]).T   # Shape: (K_DIM, M_SAMPLES)

# 3. 计算离散时间算子 P0
# 使用伪逆，更稳健
P0_T = Psi_Yf @ np.linalg.pinv(Psi_X) # 这是 Koopman 算子 K0
P0 = P0_T.T # Perron-Frobenius 算子 P0 = K0^T

# 4. 计算生成元 L0
L0 = (P0 - np.eye(K_DIM)) / DT_EDMD

# --- 2.4 计算 B1 ---
print("正在计算 B1...")
# 1. 仅在控制场下演化
Y_data_g = np.array([x + duffing_control_field(x) * DT_EDMD for x in X_data_grid])

# 2. 提升
Psi_Yg = np.array([Psi(y) for y in Y_data_g]).T

# 3. 计算 P1
P1_T = Psi_Yg @ np.linalg.pinv(Psi_X)
P1 = P1_T.T

# 4. 计算 B1
# B1 描述的是控制u=1时，由g(x)流引起的密度变化
# 注意：这实际上是L_g的近似，不是(P1-I)/dt
# 正确的做法是 L_g = L_{f+g} - L_f, 但这里可以用(P_g-I)/dt近似
B1 = (P1 - np.eye(K_DIM)) / DT_EDMD

print(f"建模完成. L0和B1的维度: {L0.shape}")

# =============================================================================
# 3. 仿真与预测 (已修正维度问题并优化)
# =============================================================================

# --- 3.1 初始密度投影 ---
# 初始PDF是高斯分布 p0 ~ N(mean_x0, cov_x0)
# 我们需要找到它在基函数空间中的坐标 rho_hat_0。
# 这是一个投影问题。一个常见的、有效的工程近似方法是：
# 1. 从p0生成大量蒙特卡洛样本
# 2. 将每个样本点通过基函数映射到高维空间
# 3. 对所有高维向量求平均，作为初始坐标向量
print("正在将初始PDF投影到基函数空间...")
initial_samples_mc = np.random.multivariate_normal(mean_x0, cov_x0, N_SAMPLES_MC)
Psi_mc_initial = np.array([Psi(x) for x in initial_samples_mc]).T # Shape: (K_DIM, N_SAMPLES_MC)
rho_hat_0 = np.mean(Psi_mc_initial, axis=1) # Shape: (K_DIM,)

# --- 3.2 KLPF 密度坐标演化 ---
def lifted_dynamics(t, rho_hat):
    """描述密度坐标rho_hat演化的双线性系统"""
    u = control_input(t)
    # 这是论文中的 Eq. 18
    d_rho_hat_dt = L0 @ rho_hat + u * (B1 @ rho_hat)
    return d_rho_hat_dt

print("正在通过KLPF模型预测密度演化...")
sol_lifted = solve_ivp(
    fun=lifted_dynamics,
    t_span=[0, T_FINAL],
    y0=rho_hat_0,
    t_eval=t_eval,
    method='RK45'
)
print(t_eval.shape)
# 预测的密度坐标随时间的变化
rho_hat_t = sol_lifted.y # Shape: (K_DIM, len(t_eval))
print(rho_hat_t.shape)

# --- 3.3 从密度坐标计算矩 (Moments) ---
def calculate_moments_from_lifted_state(rho_hat_over_time, Psi_func, integration_grid):
    """从密度坐标的时间序列高效地计算矩"""
    print("正在从预测的密度坐标计算矩...")
    moments_t = []
    
    # 预计算积分所需的Psi矩阵，避免在循环中重复计算
    Psi_grid = np.array([Psi_func(x) for x in integration_grid]).T # Shape: (K_DIM, N_GRID)
    
    for i in range(rho_hat_over_time.shape[1]): # 遍历每个时间点
        rho_hat = rho_hat_over_time[:, i]
        # 在积分网格上重构PDF的值
        pdf_values = rho_hat.T @ Psi_grid # Shape: (N_GRID,)
        pdf_values[pdf_values < 0] = 0 # 保证概率非负
        
        # 归一化，因为近似可能不保范。这确保我们计算的是一个有效PDF的矩。
        total_mass = np.sum(pdf_values)
        if total_mass > 1e-9:
            pdf_values /= total_mass
            
        # 计算矩 (向量化操作)
        mean_vec = np.sum(integration_grid * pdf_values.reshape(-1, 1), axis=0)
        # 计算二阶原点矩 E[x*x^T]
        m2_11 = np.sum(integration_grid[:, 0]**2 * pdf_values)
        m2_22 = np.sum(integration_grid[:, 1]**2 * pdf_values)
        m2_12 = np.sum(integration_grid[:, 0] * integration_grid[:, 1] * pdf_values)
        
        moments_t.append([mean_vec[0], mean_vec[1], m2_11, m2_22, m2_12])
        
    return np.array(moments_t)

predicted_moments = calculate_moments_from_lifted_state(rho_hat_t, Psi, X_data_grid)

# --- 3.4 运行蒙特卡洛仿真作为"真值" (已修正) ---
# 核心错误修正：必须为 solve_ivp 提供一个能处理所有样本的向量化动力学函数
def vectorized_duffing_dynamics(t, y_flat, u_func, n_samples):
    """
    处理所有MC样本的向量化动力学函数
    y_flat: shape (2 * n_samples,) 的一维数组
    """
    # 1. 将一维扁平化状态向量恢复为 (2, n_samples) 的矩阵
    states = y_flat.reshape((2, n_samples))
    x1s = states[0, :]
    x2s = states[1, :]
    
    # 2. 向量化计算漂移项
    drift = np.vstack([
        x2s,
        x1s - x1s**3 - DAMPING_DELTA * x2s
    ]) # Shape: (2, n_samples)

    # 3. 向量化计算控制项
    control = np.vstack([
        np.zeros(n_samples),
        np.ones(n_samples)
    ]) # Shape: (2, n_samples)
    
    # 4. 计算总导数
    u = u_func(t)
    d_states_dt = drift + control * u
    
    # 5. 将结果重新扁平化为一维数组以返回给 solve_ivp
    return d_states_dt.flatten()

print("正在运行蒙特卡洛仿真作为'真值' (已修正)...")
# 初始状态向量需要被扁平化为一维
y0_flat = initial_samples_mc.flatten()

sol_mc = solve_ivp(
    fun=vectorized_duffing_dynamics,
    t_span=[0, T_FINAL],
    y0=y0_flat,
    t_eval=t_eval,
    method='RK45',
    # 将额外参数传入动力学函数
    args=(control_input, N_SAMPLES_MC,)
)

# --- 3.5 从MC结果计算矩 (已修正) ---
# 将仿真结果从扁平化恢复为 (2, n_samples, n_times) 的形状
mc_states_t = sol_mc.y.reshape(2, N_SAMPLES_MC, -1)
mc_moments = []
mc_covariances = []

for i in range(len(t_eval)):
    # 提取当前时间点的所有样本状态
    states_at_t = mc_states_t[:, :, i].T # Shape: (n_samples, 2)
    
    # 计算均值和协方差
    mean = np.mean(states_at_t, axis=0)
    cov = np.cov(states_at_t, rowvar=False)
    
    # 从均值和协方差计算二阶原点矩 E[x*x^T] = Cov(x) + E[x]E[x]^T
    m2_matrix = cov + np.outer(mean, mean)
    
    mc_moments.append([mean[0], mean[1], m2_matrix[0, 0], m2_matrix[1, 1], m2_matrix[0, 1]])
    mc_covariances.append(cov)

mc_moments = np.array(mc_moments)
mc_covariances = np.array(mc_covariances)

sol_lifted.t[-1], Psi_mc_initial.shape

# =============================================================================
# 4. 绘图
# =============================================================================

# --- 4.1 复现 Fig. 3 ---
print("正在生成 Fig. 3...")
fig3, axes3 = plt.subplots(3, 2, figsize=(12, 10))
fig3.suptitle("Fig. 3: Moment Propagation for a Forced Duffing Oscillator", fontsize=16)

# m11 (Mean of x1)
axes3[0, 0].plot(t_eval, mc_moments[:, 0], 'b-', label='Sample Mean')
axes3[0, 0].plot(t_eval, predicted_moments[:, 0], 'g--', label='PF Prediction')
axes3[0, 0].set_ylabel('$m_1^1$ (Mean of $x_1$)')
axes3[0, 0].legend()
axes3[0, 0].grid(True)

# m12 (Mean of x2)
axes3[1, 0].plot(t_eval, mc_moments[:, 1], 'b-', label='Sample Mean')
axes3[1, 0].plot(t_eval, predicted_moments[:, 1], 'g--', label='PF Prediction')
axes3[1, 0].set_ylabel('$m_1^2$ (Mean of $x_2$)')
axes3[1, 0].grid(True)

# Control Input
axes3[2, 0].plot(t_eval, control_input(t_eval), 'r-')
axes3[2, 0].set_ylabel('Control Input u(t)')
axes3[2, 0].set_xlabel('Time (s)')
axes3[2, 0].grid(True)

# m2_11 (Second raw moment of x1)
axes3[0, 1].plot(t_eval, mc_moments[:, 2], 'b-')
axes3[0, 1].plot(t_eval, predicted_moments[:, 2], 'g--')
axes3[0, 1].set_ylabel('$m_2^{11}$')

# m2_22
axes3[1, 1].plot(t_eval, mc_moments[:, 3], 'b-')
axes3[1, 1].plot(t_eval, predicted_moments[:, 3], 'g--')
axes3[1, 1].set_ylabel('$m_2^{22}$')

# m2_12
axes3[2, 1].plot(t_eval, mc_moments[:, 4], 'b-')
axes3[2, 1].plot(t_eval, predicted_moments[:, 4], 'g--')
axes3[2, 1].set_ylabel('$m_2^{12}$')
axes3[2, 1].set_xlabel('Time (s)')

for ax in axes3[:,1]:
    ax.grid(True)
    
plt.tight_layout(rect=[0, 0, 1, 0.96])

# --- 4.2 复现 Fig. 1 ---
from matplotlib.patches import Ellipse
import matplotlib.transforms as transforms

def plot_covariance_ellipse(ax, mean, cov, n_std=2.0, facecolor='none', **kwargs):
    pearson = cov[0, 1] / np.sqrt(cov[0, 0] * cov[1, 1])
    ell_radius_x = np.sqrt(1 + pearson)
    ell_radius_y = np.sqrt(1 - pearson)
    ellipse = Ellipse((0, 0), width=ell_radius_x * 2, height=ell_radius_y * 2,
                      facecolor=facecolor, **kwargs)
    
    scale_x = np.sqrt(cov[0, 0]) * n_std
    scale_y = np.sqrt(cov[1, 1]) * n_std
    
    transf = transforms.Affine2D() \
        .rotate_deg(45) \
        .scale(scale_x, scale_y) \
        .translate(mean[0], mean[1])
        
    ellipse.set_transform(transf + ax.transData)
    return ax.add_patch(ellipse)


print("正在生成 Fig. 1...")
fig1, axes1 = plt.subplots(1, 5, figsize=(25, 5))
fig1.suptitle("Fig. 1: Moment Propagation for a Duffing Oscillator", fontsize=16)
plot_times = [0.0, 1.0, 2.0, 3.0, 4.0]
time_indices = [np.searchsorted(t_eval, t) for t in plot_times]

for i, (ax, t_idx) in enumerate(zip(axes1, time_indices)):
    # 绘制散点
    states_at_t = mc_states_t[:, :, t_idx].T
    ax.plot(states_at_t[:, 0], states_at_t[:, 1], 'r.', markersize=2, alpha=0.5)
    
    # 绘制MC的均值和协方差椭圆
    mc_mean = mc_moments[t_idx, :2]
    mc_cov = mc_covariances[t_idx]
    ax.plot(mc_mean[0], mc_mean[1], 'ro', markersize=6)
    plot_covariance_ellipse(ax, mc_mean, mc_cov, edgecolor='r', linewidth=2)

    # 绘制PF预测的均值和协方差椭圆
    pf_mean = predicted_moments[t_idx, :2]
    # 从原点矩计算协方差
    m2_matrix_pf = np.array([
        [predicted_moments[t_idx, 2], predicted_moments[t_idx, 4]],
        [predicted_moments[t_idx, 4], predicted_moments[t_idx, 3]]
    ])
    pf_cov = m2_matrix_pf - np.outer(pf_mean, pf_mean)
    ax.plot(pf_mean[0], pf_mean[1], 'ko', markersize=6)
    plot_covariance_ellipse(ax, pf_mean, pf_cov, edgecolor='k', linestyle='--', linewidth=2)
    
    ax.set_title(f"t = {plot_times[i]:.2f}")
    ax.set_xlabel("$x_1$")
    ax.set_ylabel("$x_2$")
    ax.set_xlim(-2.5, 2.5)
    ax.set_ylim(-2.5, 2.5)
    ax.grid(True)
    ax.set_aspect('equal')

plt.tight_layout(rect=[0, 0, 1, 0.95])
plt.show()