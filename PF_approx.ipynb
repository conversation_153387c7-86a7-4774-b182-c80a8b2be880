import numpy as np
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt

# =============================================================================
# 1. 案例定义 (根据论文 Sec. III.C)
# =============================================================================
# --- 杜芬振荡器动力学 ---
# 论文中没有给出阻尼系数，我们参考通常的杜芬模型，设一个小的阻尼
DAMPING_DELTA = 0.1 

def duffing_drift(x):
  """漂移项 f(x)"""
  x1, x2 = x
  return np.array([x2, x1 - x1**3 - DAMPING_DELTA * x2])

def duffing_control_field(x):
  """控制项 g(x)"""
  return np.array([0.0, 1.0])

def duffing_dynamics(t, x, u_func):
  """完整的杜芬动力学"""
  u = u_func(t)
  return duffing_drift(x) + duffing_control_field(x) * u

# --- 仿真和控制参数 ---
# 控制输入 u(t) = sin(4t)
def control_input(t):
  return np.sin(4 * t)

# 初始条件 (用于生成样本)
mean_x0 = np.array([-0.5, 1.0])
cov_x0 = np.diag([0.05, 0.05])
N_SAMPLES_MC = 1000  # 用于生成"真值"的蒙特卡洛样本数

# 仿真时间
T_FINAL = 5.0
t_eval = np.linspace(0, T_FINAL, 200)

# =============================================================================
# 2. 数据驱动建模: EDMD 计算 L0 和 B1
# =============================================================================

# --- 2.1 定义基函数字典 ---
def create_rbf_dictionary(grid_min, grid_max, num_per_dim, sigma):
  """创建高斯RBF基函数字典"""
  centers_x1 = np.linspace(grid_min, grid_max, num_per_dim)
  centers_x2 = np.linspace(grid_min, grid_max, num_per_dim)
  grid_centers = np.array(np.meshgrid(centers_x1, centers_x2)).T.reshape(-1, 2)
  
  # Psi(x) 是一个返回向量的函数
  def Psi(x):
    diff = grid_centers - x
    sq_dist = np.sum(diff**2, axis=1)
    return np.exp(-sq_dist / (2 * sigma**2))

  return Psi, grid_centers

# 论文中指定的字典参数
GRID_MIN, GRID_MAX = -2.5, 2.5
NUM_PER_DIM = 30
SIGMA_RBF = 0.5 # RBF宽度，可以调整
Psi, centers = create_rbf_dictionary(GRID_MIN, GRID_MAX, NUM_PER_DIM, SIGMA_RBF)
K_DIM = len(centers) # 字典维度: 30*30 = 900

# --- 2.2 生成建模数据 ---
print("正在生成用于EDMD建模的数据...")
DT_EDMD = 0.005 # 论文指定的时间步长
# 论文指定使用 50x50 网格生成初始点
num_grid_points = 50
x1_grid = np.linspace(GRID_MIN, GRID_MAX, num_grid_points)
x2_grid = np.linspace(GRID_MIN, GRID_MAX, num_grid_points)
X_data_grid = np.array(np.meshgrid(x1_grid, x2_grid)).T.reshape(-1, 2)
M_SAMPLES = len(X_data_grid)

# --- 2.3 计算 L0 ---
print("正在计算 L0...")
# 1. 仅在漂移场下演化
Y_data_f = np.array([x + duffing_drift(x) * DT_EDMD for x in X_data_grid])

# 2. 将数据点提升到基函数空间
Psi_X = np.array([Psi(x) for x in X_data_grid]).T  # Shape: (K_DIM, M_SAMPLES)
Psi_Yf = np.array([Psi(y) for y in Y_data_f]).T   # Shape: (K_DIM, M_SAMPLES)

# 3. 计算离散时间算子 P0
# 使用伪逆，更稳健
P0_T = Psi_Yf @ np.linalg.pinv(Psi_X) # 这是 Koopman 算子 K0
P0 = P0_T.T # Perron-Frobenius 算子 P0 = K0^T

# 4. 计算生成元 L0
L0 = (P0 - np.eye(K_DIM)) / DT_EDMD

# --- 2.4 计算 B1 ---
print("正在计算 B1...")
# 1. 仅在控制场下演化
Y_data_g = np.array([x + duffing_control_field(x) * DT_EDMD for x in X_data_grid])

# 2. 提升
Psi_Yg = np.array([Psi(y) for y in Y_data_g]).T

# 3. 计算 P1
P1_T = Psi_Yg @ np.linalg.pinv(Psi_X)
P1 = P1_T.T

# 4. 计算 B1
# B1 描述的是控制u=1时，由g(x)流引起的密度变化
# 注意：这实际上是L_g的近似，不是(P1-I)/dt
# 正确的做法是 L_g = L_{f+g} - L_f, 但这里可以用(P_g-I)/dt近似
B1 = (P1 - np.eye(K_DIM)) / DT_EDMD

print(f"建模完成. L0和B1的维度: {L0.shape}")

# =============================================================================
# 3. 仿真与预测
# =============================================================================

# --- 3.1 初始密度投影 ---
# 初始PDF是高斯分布 p0 ~ N(mean_x0, cov_x0)
# 我们需要找到它的坐标 rho_hat_0
# rho_hat_0 = argmin || Psi(x)^T * rho_hat - p0(x) ||^2
# 解是 rho_hat_0 = (Psi Psi^T)^-1 * (Psi * p0)
# 这需要对空间积分，一个更简单的方法是使用初始样本的权重
initial_samples_mc = np.random.multivariate_normal(mean_x0, cov_x0, N_SAMPLES_MC)
Psi_mc_initial = np.array([Psi(x) for x in initial_samples_mc]).T
# 简单的加权平均来近似投影
rho_hat_0 = np.mean(Psi_mc_initial, axis=1)

# --- 3.2 KLPF 密度坐标演化 ---
def lifted_dynamics(t, rho_hat):
  u = control_input(t)
  # 这是论文中的 Eq. 18
  d_rho_hat_dt = L0 @ rho_hat + u * (B1 @ rho_hat)
  return d_rho_hat_dt

print("正在通过KLPF模型预测密度演化...")
sol_lifted = solve_ivp(
  fun=lifted_dynamics,
  t_span=[0, T_FINAL],
  y0=rho_hat_0,
  t_eval=t_eval,
  method='RK45'
)
rho_hat_t = sol_lifted.y

# --- 3.3 从密度坐标计算矩 (Moments) ---
def calculate_moments(rho_hat_t, Psi_func, integration_grid):
  """从密度坐标计算矩"""
  moments_t = []
  
  # 预计算积分所需的Psi矩阵
  Psi_grid = np.array([Psi_func(x) for x in integration_grid]).T # (K_DIM, N_GRID)
  
  for i in range(rho_hat_t.shape[1]): # 遍历每个时间点
    rho_hat = rho_hat_t[:, i]
    # 近似PDF在网格上的值
    pdf_values = rho_hat.T @ Psi_grid
    pdf_values[pdf_values < 0] = 0 # 保证非负
    
    # 归一化，因为近似可能不保范
    total_mass = np.sum(pdf_values)
    if total_mass > 1e-6:
      pdf_values /= total_mass
        
    # 计算矩
    m1 = np.sum(integration_grid * pdf_values.reshape(-1, 1), axis=0)
    m2_11 = np.sum(integration_grid[:, 0]**2 * pdf_values)
    m2_22 = np.sum(integration_grid[:, 1]**2 * pdf_values)
    m2_12 = np.sum(integration_grid[:, 0] * integration_grid[:, 1] * pdf_values)
    
    moments_t.append([m1[0], m1[1], m2_11, m2_22, m2_12])
      
  return np.array(moments_t)

print("正在从预测的密度坐标计算矩...")
predicted_moments = calculate_moments(rho_hat_t, Psi, X_data_grid)

# --- 3.4 运行蒙特卡洛仿真作为"真值" ---
print("正在运行蒙特卡洛仿真作为'真值'...")
sol_mc = solve_ivp(
  fun=lambda t, x: duffing_dynamics(t, x, control_input),
  t_span=[0, T_FINAL],
  y0=initial_samples_mc.flatten(), # 展平初始状态
  t_eval=t_eval,
  method='RK45'
)

# --- 3.5 从MC结果计算矩 ---
mc_states_t = sol_mc.y.reshape(2, N_SAMPLES_MC, -1)
mc_moments = np.zeros((len(t_eval), 5))
mc_covariances = np.zeros((len(t_eval), 2, 2))
sample_trajectories = [] # 用于绘制Fig.1

for i in range(len(t_eval)):
  states = mc_states_t[:, :, i].T
  if i==0:
    sample_trajectories.append(states)
      
  mean = np.mean(states, axis=0)
  cov = np.cov(states, rowvar=False)
  
  mc_moments[i, 0] = mean[0]
  mc_moments[i, 1] = mean[1]
  # 二阶原点矩 E[x*x^T] = Cov(x) + E[x]E[x]^T
  m2_matrix = cov + np.outer(mean, mean)
  mc_moments[i, 2] = m2_matrix[0, 0]
  mc_moments[i, 3] = m2_matrix[1, 1]
  mc_moments[i, 4] = m2_matrix[0, 1]
  mc_covariances[i] = cov

# 收集所有时间点的轨迹用于绘图
for i, t in enumerate(t_eval):
  if i > 0 and t > 0:
    states_at_t = mc_states_t[:, :, i].T
    sample_trajectories.append(states_at_t)

initial_samples_mc.shape