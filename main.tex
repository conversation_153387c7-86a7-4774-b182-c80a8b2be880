\documentclass[a4paper, 11pt]{article}
\usepackage[english]{babel}
\usepackage[utf8x]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{scribe}
\usepackage{listings}
\usepackage{nomencl}
\makenomenclature
\usepackage{color}
\usepackage[colorlinks, linkcolor=blue, anchorcolor=green, citecolor=magenta]{hyperref}
\usepackage{mathtools}
\usepackage{amsmath}
\usepackage{bbm}
\usepackage{natbib}
\usepackage{algorithm, algorithmicx, algpseudocode}
\usepackage{wrapfig} % wrap figure in texts

\usepackage{cancel}
\renewcommand{\CancelColor}{\color{red}}

% \usepackage{amsmath}
% \newtheorem{theorem}{Theorem}
% \newtheorem{lemma}{Lemma}
% \newtheorem{proof}{Proof}[section]

% \usepackage{enumerate}[itemindent=4em] % narrow down the space before enumate
\usepackage{enumitem}
\setlength{\abovecaptionskip}{-.5cm} % skip blanck space before caption
\numberwithin{equation}{section} % number equation with section

\lstset{style=mystyle}

\begin{document}

%#############################################################
%#############################################################
\Scribe{Shen Fang}
\Lecturer{Shen Fang}
\LectureDate{2025/08/09} % \today
\LectureTitle{Perron-Frobenius Operator}
\MakeScribeTop
\setlength{\abovedisplayskip}{5pt}
\setlength{\belowdisplayskip}{5pt}
\tableofcontents

\newpage


%#############################################################
%#############################################################
\section{Definitions}

For a dynamical system $\dot{x} = f(x)$, with PDF $p(t, x)$, the Perron-Frobenius operator $\mathcal{P}$ is a Markov operator defined as
\begin{equation}
p(t, x) \coloneqq \mathcal{P}_t p(t_0, x)
\end{equation}
which satisfies the following properties \citep{lasota2013chaos}:
\begin{enumerate}
\item Continuity
\begin{equation}
\lim_{t \to t_0} \Vert \mathcal{P}_t p(t_0, x) - p(t_0, x) \Vert = 0
\end{equation}
\item Linear
\begin{equation}
\mathcal{P}_t (a p_1(t_0, x) + b p_2(t_0, x)) 
= a \mathcal{P}_t p_1(t_0, x) + b \mathcal{P}_t p_2(t_0, x)
\end{equation}
where $a, b \in \mathbb{R}$ and $p_1(t_0, x), p_2(t_0, x) \in \mathcal{L}^1$.
\item Positivity
\begin{equation}
\mathcal{P}_t p(t_0, x) \geq 0 \quad \forall p(t_0, x) \geq 0
\end{equation}
\item Norm preserving
\begin{equation}
\int_\mathcal{X} \mathcal{P}_t p(t_0, x) \mu \, \mathrm{d}x 
= \int_\mathcal{X} p(t_0, x) \mu \, \mathrm{d}x
\end{equation}
\end{enumerate}

Notice that $\mathcal{L}^1$ is a set of functions $f: \mathcal{X} \rightarrow \mathbb{R}$ satisfying
\begin{equation}
\int_\mathcal{X} \vert f(x) \vert \mu \, \mathrm{d}x < \infty
\end{equation}

Perrson-Frobenius operator $\mathcal{P}_t$ forms a \textit{semigroup}, i.e., $\mathcal{P}_{t+s} = \mathcal{P}_t \circ \mathcal{P}_s$. So it has corresponding infinitesimal generator $\mathcal{L}$ defined as
\begin{equation}
\mathcal{L}(p) = \lim_{t \to 0^+} \frac{\mathcal{P}_t p - p}{t} = \frac{\partial p}{\partial t}
\end{equation}


%#############################################################
%#############################################################
\section{Deterministic Dynamics}

\subsection{Liouville equation}
We will show that in \textit{deterministic dynamics}, the Perron-Frobenius operator follows the \textit{Liouville equation}.

For a deterministic dynamical system $\dot{x} = f(x)$, we denote the flow as $x(t) = \phi_t(x_0)$. Due to the conservation of probability, for any $\mathcal{A} \subset \mathcal{X}$, we have
\begin{equation}
\int_\mathcal{A} \left(\mathcal{P}_t p_0\right)(x) \mathrm{d}x 
= \int_{\phi_{t}^{-1}(\mathcal{A})} p_0(x_0) \mathrm{d}x_0
\end{equation}
Define the Jacobian determinant as $|J_{-t}(x)| = |\det(D_x \phi_{-t}(x))|$, we have
\begin{equation}
\left(\mathcal{P}_t p_0\right)(x) = p_0(\phi_{-t}(x)) \cdot |J_{-t}(x)|
\end{equation}
The infinitesimal flow is given by the dynamics
\begin{equation}
\lim_{t \to 0^+} \phi_{-t}(x) = x - t f(x)
\end{equation}
\begin{equation}
\lim_{t \to 0^+} p_0(\phi_{-t}(x)) = p_0(x - t f(x))
= p_0(x) - t \nabla p_0(x) \cdot f(x)
\end{equation}
The dynamics of Jacobian determinant is given by \textcolor{red}{\textit{Liouville's formula}} as
\begin{equation}
\frac{\mathrm{d} |J_{t}(x_0)|}{\mathrm{d}t} = (\nabla \cdot f) (\phi_t(x_0)) \cdot |J_{t}(x_0)|
\label{eq:liouville_formula}
\end{equation}
The infinitesimal Jacobian determinant is given by
\begin{equation}
\lim_{t \to 0^+} |J_{-t}(x)| = 1 - (\nabla \cdot f)(x) \cdot t
\end{equation}
The infinitesimal generator is given by
\begin{equation}
\mathcal{L} p = \lim_{t \to 0^+} \frac{\mathcal{P}_t p - p}{t} 
= \frac{[p - t \nabla p \cdot f] \cdot [1 - (\nabla \cdot f) \cdot t] - p}{t}
= -\nabla p \cdot f - p \nabla \cdot f = -\nabla \cdot (p f)
\end{equation}
where we reached the \textcolor{red}{\textit{Liouville equation}} 
\begin{equation}
\frac{\partial p}{\partial t} = -\nabla \cdot (p f)
\end{equation}

\subsection{Method of characteristics}
Given the deterministic dynamics $\dot{x} = f(x)$, we can solve the density evolution using the \textcolor{red}{\textit{method of characteristics}}. The Liouville equation can be written as
\begin{equation}
\begin{aligned}
& \, \frac{\partial p(t, x)}{\partial t} + \sum_{i=1}^{n} 
\frac{\partial}{\partial x_i} \left[ p(t, x) f_i(x) \right] = 0  \\
\Rightarrow & \,
\frac{\partial p(t, x)}{\partial t} + \sum_{i=1}^{n} 
f_i(x) \frac{\partial p(t, x)}{\partial x_i} = 
- p(t, x) \sum_{i=1}^{n} \frac{\partial f_i(x)}{\partial x_i}
\end{aligned}
\end{equation}
which is a PDE of the form
\begin{equation}
a(t, x, p) \frac{\partial p}{\partial t} 
+ \sum_{i=1}^{n} b_i(t, x, p) \frac{\partial p}{\partial x_i} 
= c(t, x, p) 
\end{equation}
The PDE can be solved by the \textcolor{red}{\textit{Lagrange-Charpit equations}} as
\begin{equation}
\frac{\mathrm{d}t}{a(t, x, p)} = \frac{\mathrm{d}x_i}{b_i(t, x, p)} = \frac{\mathrm{d}p}{c(t, x, p)} = \mathrm{d} s
\Rightarrow 
\frac{\mathrm{d}t}{1} = \frac{\mathrm{d}x_i}{f_i(t, x)} = \frac{\mathrm{d}p}{-p \sum_{i=1}^{n} \frac{\partial f_i(x)}{\partial x_i}} = \mathrm{d} s
\end{equation}
The Liouville equation can be reduced to an ODE as
\begin{equation}
\frac{\mathrm{d} p(t, x)}{\mathrm{d}t} = -p(t, x) \sum_{i=1}^{n} \frac{\partial f_i(x)}{\partial x_i}
\end{equation}
Given the initial joint PDF $p_0(0, x)$, the solution is given by
\begin{equation}
p(t, x) = p_0(0, x) \cdot \exp\left(-\int_0^t \sum_{i=1}^{n} \frac{\partial f_i(x(s))}{\partial x_i} \mathrm{d}s\right)
\end{equation}




%#############################################################
%#############################################################
\newpage
\appendix


%#############################################################
%#############################################################
\section{Proofs}

\subsection{Liouville's Formula \refeq{eq:liouville_formula}}
We now derive the Liouville's formula, a.k.a \textit{Abel-Liouville-Ostrogradski Identity}. 

\begin{proof}
Starting from the flow equation
\begin{equation}
\frac{\mathrm{d} \phi_t(x_0)}{\mathrm{d}t} {\color{red}=} f(\phi_t(x_0))
\end{equation}
Taking the derivative over $x_0$, we have
\begin{equation}
\frac{\mathrm{d}}{\mathrm{d}t} (D_{x_0} \phi_t(x_0))
= D_{x_0} \left(\frac{\mathrm{d} \phi_t(x_0)}{\mathrm{d}t}\right)
{\color{red}=} D_{x_0} (f(\phi_t(x_0)))
= (D_{x_0} f) (\phi_t(x_0)) \cdot D_{x_0} \phi_t(x_0) 
\end{equation}
We have the \textit{First Variational equation} as
\begin{equation}
\frac{\mathrm{d}}{\mathrm{d}t} J_t(x_0)
{\color{red}=} (D_{x_0} f) (\phi_t(x_0)) \cdot J_t(x_0)
\end{equation}
For any time-varying vertible matrix $A(t)$, we have
\begin{equation}
\frac{\mathrm{d}}{\mathrm{d}t} \det(A(t)) = \det(A(t)) \cdot \mathrm{tr}\left(A(t)^{-1} \frac{\mathrm{d} A(t)}{\mathrm{d}t}\right)
\end{equation}
For the Jacobian matrix $J_t(x_0)$, we have
\begin{equation}
\begin{aligned}
\frac{\mathrm{d}}{\mathrm{d}t} |J_t(x_0)| 
&= |J_t(x_0)| \cdot \mathrm{tr}\left(J_t(x_0)^{-1} \frac{\mathrm{d} J_t(x_0)}{\mathrm{d}t}\right) \\
&= |J_t(x_0)| \cdot \underbrace{\mathrm{tr}\left(J_t(x_0)^{-1} \cdot (D_{x_0} f) (\phi_t(x_0))  \cdot J_t(x_0)\right)}_{\color{blue} \mathrm{tr}(ABC) = \mathrm{tr}(BCA)} \\
&= |J_t(x_0)| \cdot \mathrm{tr}\left((D_{x_0} f) (\phi_t(x_0)) \cdot J_t(x_0) \cdot J_t(x_0)^{-1}\right) \\
&= |J_t(x_0)| \cdot \underbrace{\mathrm{tr}\left((D_{x_0} f)\right)}_{\color{blue} = \sum_{i=1}^{n} \frac{\partial f_i}{\partial x_i}} (\phi_t(x_0)) \\
&= |J_t(x_0)| \cdot (\nabla \cdot f) (\phi_t(x_0))
\end{aligned}
\end{equation}
Since the physical meaning of the Jacobian determinant is the relative volume expansion rate, the Liouville's formula implies that \textcolor{blue}{\textit{the relative volume expansion rate is equal to the divergence of the flow}}.
\end{proof}



%%%%%%%%%%% If you don't have citations then comment the lines below:
%
\bibliographystyle{abbrvnat}           % if you need a bibliography
\bibliography{refs}                % assuming yours is named mybib.bib


%%%%%%%%%%% end of doc
\end{document}