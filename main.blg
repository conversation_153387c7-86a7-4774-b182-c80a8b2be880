This is BibTeX, Version 0.99d (TeX Live 2022)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
The style file: abbrvnat.bst
Database file #1: refs.bib
You've used 5 entries,
            2773 wiz_defined-function locations,
            622 strings with 5506 characters,
and the built_in function-call counts, 2316 in all, are:
= -- 208
> -- 103
< -- 7
+ -- 35
- -- 30
* -- 156
:= -- 338
add.period$ -- 16
call.type$ -- 5
change.case$ -- 23
chr.to.int$ -- 5
cite$ -- 10
duplicate$ -- 127
empty$ -- 207
format.name$ -- 41
if$ -- 507
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 7
newline$ -- 33
num.names$ -- 20
pop$ -- 61
preamble$ -- 1
purify$ -- 20
quote$ -- 0
skip$ -- 102
stack$ -- 0
substring$ -- 83
swap$ -- 28
text.length$ -- 3
text.prefix$ -- 0
top$ -- 0
type$ -- 45
warning$ -- 0
while$ -- 21
width$ -- 0
write$ -- 72
