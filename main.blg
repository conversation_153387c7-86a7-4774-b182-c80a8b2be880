This is BibTeX, Version 0.99d (TeX Live 2022)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
The style file: abbrvnat.bst
Database file #1: refs.bib
You've used 1 entry,
            2773 wiz_defined-function locations,
            598 strings with 4690 characters,
and the built_in function-call counts, 401 in all, are:
= -- 29
> -- 21
< -- 2
+ -- 7
- -- 6
* -- 28
:= -- 68
add.period$ -- 3
call.type$ -- 1
change.case$ -- 4
chr.to.int$ -- 1
cite$ -- 2
duplicate$ -- 20
empty$ -- 33
format.name$ -- 9
if$ -- 78
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 2
newline$ -- 13
num.names$ -- 4
pop$ -- 12
preamble$ -- 1
purify$ -- 4
quote$ -- 0
skip$ -- 17
stack$ -- 0
substring$ -- 2
swap$ -- 2
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 6
warning$ -- 0
while$ -- 3
width$ -- 0
write$ -- 20
