
import numpy as np
import sympy as sp
from scipy.integrate import solve_ivp
from scipy.stats import gaussian_kde
import matplotlib.pyplot as plt
from matplotlib import rcParams
config = {
  "font.family":'sans serif',
  "font.size": 12,
  "mathtext.fontset":'custom',
  "font.sans-serif": ['Arial'],
  "axes.unicode_minus":False,
}
rcParams.update(config)

# 物理常数 (火星)
R0 = 3397e3  # 平均半径 [m]
GM = 4.2828e13 # 引力常数 [m^3/s^2]
g0 = GM / R0**2 # 表面重力加速度 [m/s^2]
vc = 5.027e3       # 火星逃逸速度 [m/s]

# 航天器和大气模型参数
Bc_nom = 72.8      # 弹道系数 [kg/m^2]
CL_CD_nom = 0.3    # 升阻比
rho0_nom = 0.0019  # 参考大气密度 [kg/m^3]
h1 = 9800.0        # 大气模型参数 [m]
h2 = 20000.0       # 大气模型参数 [m]

# 初始条件 (标称值)
h0_nom = 80e3      # 初始高度 [m]
V0_nom = 3500.0    # 初始速度 [m/s]
gamma0_nom = -2.0 * np.pi / 180.0 # 初始飞行路径角 [rad]

# 不确定性设置 (Fig. 8 使用均匀分布)
UNCERTAINTY_FACTOR = 0.05 # 5% 的不确定性

# 仿真参数
N_SAMPLES = 5000 # 论文中使用的样本数
T_FACTOR = R0 / vc # 物理时间到无量纲时间的转换因子

# 我们要复现的时间点 (无量纲时间)
t_eval_nondim = [0.05, 0.30, 0.50]
t_final_nondim = t_eval_nondim[-1]

# 将参数打包
params = {
  'R0': R0, 'GM': GM, 'g0': g0, 'vc': vc,
  'Bc': Bc_nom, 'CL_CD': CL_CD_nom,
  'rho0': rho0_nom, 'h1': h1, 'h2': h2
}

# 大气密度模型
def get_rho(h, p):
  return p['rho0'] * np.exp(-(h - p['h2']) / p['h1'])

# 3状态Vinh方程
def vinh_dynamics(t, y, p):
  h, V, gamma = y
  
  rho = p['rho0'] * np.exp((p['h2'] - h * p['R0']) / p['h1'])
  dh_dt = V * np.sin(gamma)
  dV_dt = - (rho * p['R0'] * V**2) / (2 * p['Bc']) - \
            (p['g0'] * p['R0'] * np.sin(gamma) / p['vc']**2)
  dgamma_dt = (rho * p['R0'] * V * p['CL_CD']) / (2 * p['Bc']) + \
            (p['g0'] * p['R0'] * np.cos(gamma) / p['vc']**2) * (V / (1 + h) - 1 / V)

  return np.array([dh_dt, dV_dt, dgamma_dt])

# 使用 Sympy 自动计算散度 (Divergence)
def get_divergence_func(p):
  """使用符号计算库自动推导散度，避免手动错误"""
  h_s, V_s, gamma_s = sp.symbols('h V gamma')

  rho = p['rho0'] * sp.exp((p['h2'] - h_s * p['R0']) / p['h1'])
  F_h = V_s * sp.sin(gamma_s)
  F_V = - (rho * p['R0'] * V_s**2) / (2 * p['Bc']) - \
          (p['g0'] * p['R0'] * sp.sin(gamma_s) / p['vc']**2)
  F_gamma = (rho * p['R0'] * V_s * p['CL_CD']) / (2 * p['Bc']) + \
          (p['g0'] * p['R0'] * sp.cos(gamma_s) / p['vc']**2) * (V_s / (1 + h_s) - 1 / V_s)
  F = sp.Matrix([F_h, F_V, F_gamma])
  
  # 计算散度 div(F) = dF_h/dh + dF_V/dV + dF_gamma/dgamma
  divF = F[0].diff(h_s) + F[1].diff(V_s) + F[2].diff(gamma_s)
  state_symbols = [h_s, V_s, gamma_s]
  divF_func = sp.lambdify(state_symbols, divF, 'numpy')

  return divF_func

# 获得散度函数
divergence_func = get_divergence_func(params)

# SLE 增广系统的动力学
def sle_dynamics(t, y_aug, p):
  """
  y_aug = [h, V, gamma, phi]
  dphi/dt = -phi * divergence(F)
  """
  h, V, gamma, phi = y_aug
  
  # 状态的导数
  d_state_dt = vinh_dynamics(t, [h, V, gamma], p)
  
  # 概率密度的导数
  divF = divergence_func(h, V, gamma)
  dphi_dt = -phi * divF
  
  return np.array([d_state_dt[0], d_state_dt[1], d_state_dt[2], dphi_dt])

np.random.seed(42) # for reproducibility

# 计算均匀分布的半宽
h_width = h0_nom * UNCERTAINTY_FACTOR
V_width = V0_nom * UNCERTAINTY_FACTOR
gamma_width = abs(gamma0_nom) * UNCERTAINTY_FACTOR

# 从均匀分布中采样
h_initial = np.random.uniform(h0_nom - h_width, h0_nom + h_width, N_SAMPLES)
V_initial = np.random.uniform(V0_nom - V_width, V0_nom + V_width, N_SAMPLES)
gamma_initial = np.random.uniform(gamma0_nom - gamma_width, gamma0_nom + gamma_width, N_SAMPLES)

# 对于均匀分布，初始概率密度phi_0是常数
volume = (2 * h_width) * (2 * V_width) * (2 * gamma_width) / R0 / vc
phi0 = 1.0 / volume
phi_initial = np.full(N_SAMPLES, phi0)

h_initial = h_initial / R0
V_initial = V_initial / vc
gamma_initial = gamma_initial
initial_states = np.vstack([h_initial, V_initial, gamma_initial]).T

# 组合成SLE的初始增广状态
initial_states_aug = np.hstack([initial_states, phi_initial.reshape(-1, 1)])

# 存储结果
results_mc = {t: [] for t in t_eval_nondim}
results_sle = {t: [] for t in t_eval_nondim}

t_span_nondim = [0, t_final_nondim]

print(f"开始进行 {N_SAMPLES} 次仿真...")

# --- SLE/PF 方法 ---
print("正在运行 SLE/PF 方法...")
for i, y0_aug in enumerate(initial_states_aug):
  sol = solve_ivp(
    fun=lambda t, y: sle_dynamics(t, y, params),
    t_span=t_span_nondim,
    y0=y0_aug,
    t_eval=t_eval_nondim,
    method='RK45'
  )
  for t_idx, t in enumerate(t_eval_nondim):
    results_sle[t].append(sol.y[:, t_idx])
  if (i + 1) % 500 == 0:
    print(f"  SLE: 已完成 {i+1}/{N_SAMPLES}")

# --- MC 方法 ---
print("正在运行 MC 方法...")
for i, y0 in enumerate(initial_states):
  sol = solve_ivp(
    fun=lambda t, y: vinh_dynamics(t, y, params),
    t_span=t_span_nondim,
    y0=y0,
    t_eval=t_eval_nondim,
    method='RK45'
  )
  for t_idx, t in enumerate(t_eval_nondim):
    results_mc[t].append(sol.y[:, t_idx])
  if (i + 1) % 500 == 0:
    print(f"  MC: 已完成 {i+1}/{N_SAMPLES}")

# 转换结果为Numpy数组，方便处理
for t in t_eval_nondim:
  results_mc[t] = np.array(results_mc[t])
  results_sle[t] = np.array(results_sle[t])

fig, axes = plt.subplots(3, 3, figsize=(9, 9)) # , constrained_layout=True)
fig.suptitle("SLE/PF vs. MC univariate marginals", fontsize=16)

labels = ['h (km)', 'V (km/s)', r"$\gamma$ (deg)"]
variables = [0, 1, 2] # 对应 h, V, gamma

for i, t in enumerate(t_eval_nondim):
  for j, var_idx in enumerate(variables):
    ax = axes[i, j]
    
    # 提取数据
    data_mc = np.copy(results_mc[t][:, var_idx])
    data_sle = np.copy(results_sle[t][:, var_idx])
    weights_sle = np.copy(results_sle[t][:, 3]) # 这是SLE计算出的phi值

    # 单位转换，方便阅读
    if var_idx == 0: # 高度 km
      data_mc *= R0 / 1000
      data_sle *= R0 / 1000
    elif var_idx == 1: # 速度 km/s
      data_mc *= vc / 1000
      data_sle *= vc / 1000
    elif var_idx == 2: # 飞行路径角 deg
      data_mc *= 180 / np.pi
      data_sle *= 180 / np.pi

    # ------------------
    # 绘制 MC 结果 (KDE)
    # ------------------
    try:
      kde = gaussian_kde(data_mc)
      x_range = np.linspace(data_mc.min(), data_mc.max(), 200)
      l1, = ax.plot(x_range, kde(x_range), 'k--')
    except np.linalg.LinAlgError:
      # 如果数据点太集中，KDE会失败
      l1, = ax.hist(data_mc, bins=50, density=True, histtype='step', color='k', linestyle='--')

    # ------------------
    # 绘制 SLE/PF 结果 (加权直方图)
    # ------------------
    # SLE方法直接给出了每个点的概率密度，用加权直方图可视化最准确
    _, _, l2 = ax.hist(data_sle, bins=50, density=True, weights=weights_sle, 
            histtype='step', color='k', linewidth=1.5)
    
    # --- 美化图像 ---
    ax.grid(True, linestyle=':', alpha=0.6)
    if i == 2:
      ax.set_xlabel(labels[j])
    # 在第一列设置行标题
    if j == 0:
      ax.set_ylabel("PDF")
      ax.text(-0.35, 0.5, r"$t$ = " + f"{t}", transform=ax.transAxes, 
              ha='center', va='center', rotation=90, fontsize=12)

fig.legend(handles=[l1, l2[0]], labels=['MC (KDE)', 'SLE/PF (Weighted Hist)'],           
           bbox_to_anchor=(0.5, -0.025), ncol=2, framealpha=1, loc='lower center'
)
plt.tight_layout()
plt.show()