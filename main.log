This is XeTeX, Version 3.141592653-2.6-0.999994 (TeX Live 2022) (preloaded format=xelatex 2022.10.21)  10 AUG 2025 14:59
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**main
(./main.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-02-24> (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count181
\c@section=\count182
\c@subsection=\count183
\c@subsubsection=\count184
\c@paragraph=\count185
\c@subparagraph=\count186
\c@figure=\count187
\c@table=\count188
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2022/02/26 3.73 The Babel package
\babel@savecnt=\count189
\U@D=\dimen139
\l@unhyphenated=\language87
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/babel/xebabel.def (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/babel/txtbabel.def))
\bbl@readstream=\read2
\bbl@dirlevel=\count190
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language21). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language21). Reported on input line 108.
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks16
\inpenc@posthook=\toks17


Package inputenc Warning: inputenc package ignored with utf8 based engines.

) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+lmr on input line 112.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)) (./scribe.sty (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count191
\Gm@cntv=\count192
\c@Gm@tempcnt=\count193
\Gm@bindingoffset=\dimen140
\Gm@wd@mp=\dimen141
\Gm@odd@mp=\dimen142
\Gm@even@mp=\dimen143
\Gm@layoutwidth=\dimen144
\Gm@layoutheight=\dimen145
\Gm@layouthoffset=\dimen146
\Gm@layoutvoffset=\dimen147
\Gm@dimlist=\toks19
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks20
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip49

For additional information on amsmath, use the `?' option.
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen148
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen149
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count194
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count195
\leftroot@=\count196
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count197
\DOTSCASE@=\count198
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box50
\strutbox@=\box51
\big@size=\dimen150
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count199
\c@MaxMatrixCols=\count266
\dotsspace@=\muskip16
\c@parentequation=\count267
\dspbrk@lvl=\count268
\tag@help=\toks22
\row@=\count269
\column@=\count270
\maxfields@=\count271
\andhelp@=\toks23
\eqnshift@=\dimen151
\alignsep@=\dimen152
\tagshift@=\dimen153
\tagwidth@=\dimen154
\totwidth@=\dimen155
\lineht@=\dimen156
\@envbody=\toks24
\multlinegap=\skip50
\multlinetaggap=\skip51
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks26
\thm@bodyfont=\toks27
\thm@headfont=\toks28
\thm@notefont=\toks29
\thm@headpunct=\toks30
\thm@preskip=\skip52
\thm@postskip=\skip53
\thm@headsep=\skip54
\dth@everypar=\toks31
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/latexsym.sty
Package: latexsym 1998/08/17 v2.2e Standard LaTeX package (lasy symbols)
\symlasy=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lasy' in version `bold'
(Font)                  U/lasy/m/n --> U/lasy/b/n on input line 52.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/epsfig.sty
Package: epsfig 2017/06/25 v1.7b (e)psfig emulation (SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2021/03/18 v5.0k Graphics/color driver for xetex
))
\Gin@req@height=\dimen157
\Gin@req@width=\dimen158
)
\epsfxsize=\dimen159
\epsfysize=\dimen160
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2021/04/25 v1.2e Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup7
\symboldletters=\mathgroup8
\symboldsymbols=\mathgroup9
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
\symboldlasy=\mathgroup10
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 112.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/psnfss/t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count272
\lst@gtempboxa=\box52
\lst@token=\toks32
\lst@length=\count273
\lst@currlwidth=\dimen161
\lst@column=\count274
\lst@pos=\count275
\lst@lostspace=\dimen162
\lst@width=\dimen163
\lst@newlines=\count276
\lst@lineno=\count277
\lst@maxwidth=\dimen164
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2020/03/24 1.8d (Carsten Heinz)
\c@lstnumber=\count278
\lst@skipnumbers=\count279
\lst@framebox=\box53
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2020/03/24 1.8d listings configuration
))
Package: listings 2020/03/24 1.8d (Carsten Heinz)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/color.sty
Package: color 2021/12/07 v1.3c Standard LaTeX Color (DPC)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/listings/lstlang1.sty
File: lstlang1.sty 2020/03/24 1.8d listings language file
)
\c@theorem=\count280
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 1995/11/23 v1.03 Indent first paragraph (DPC)
)
\headerwidth=\dimen165
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/nomencl/nomencl.sty
Package: nomencl 2021/11/10 v5.6 Nomenclature package
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2020/11/20 v2.8 package option processing (HA)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/xkeyval/xkeyval.tex (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks33
\XKV@tempa@toks=\toks34
)
\XKV@depth=\count281
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/koma-script/tocbasic.sty
Package: tocbasic 2021/11/13 v3.35 KOMA-Script package (handling toc-files)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2021/11/13 v3.35 KOMA-Script package (KOMA-Script-independent basics and keyval usage)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2021/11/13 v3.35 KOMA-Script package (file load hooks)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2021/11/13 v3.35 KOMA-Script package (using LaTeX hooks)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2021/11/13 v3.35 KOMA-Script package (logo)
)))
Applying: [2021/05/01] Usage of raw or classic option list on input line 252.
Already applied: [0000/00/00] Usage of raw or classic option list on input line 368.
)
\scr@dte@tocline@numberwidth=\skip55
\scr@dte@tocline@numbox=\box54
)
Package tocbasic Info: setting babel extension for `nlo' on input line 187.
Package tocbasic Info: setting babel extension for `nls' on input line 188.
\nomlabelwidth=\dimen166
\nom@tempdim=\dimen167
\nomitemsep=\skip56
)
\@nomenclaturefile=\write3
\openout3 = `main.nlo'.

Package nomencl Info: Writing nomenclature file main.nlo on input line 8.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2022-02-21 v7.00n Hypertext links for LaTeX
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen168
\Hy@linkcounter=\count282
\Hy@pagecounter=\count283
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2022-02-21 v7.00n Hyperref: PDFDocEncoding definition (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count284
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2022-02-21 v7.00n Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4018.
Package hyperref Info: Hyper figures OFF on input line 4137.
Package hyperref Info: Link nesting OFF on input line 4142.
Package hyperref Info: Hyper index ON on input line 4145.
Package hyperref Info: Plain pages OFF on input line 4152.
Package hyperref Info: Backreferencing OFF on input line 4157.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4390.
\c@Hy@tempcnt=\count285
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4749.
\XeTeXLinkMargin=\dimen169
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count286
\Field@Width=\dimen170
\Fld@charsize=\dimen171
Package hyperref Info: Hyper figures OFF on input line 6027.
Package hyperref Info: Link nesting OFF on input line 6032.
Package hyperref Info: Hyper index ON on input line 6035.
Package hyperref Info: backreferencing OFF on input line 6042.
Package hyperref Info: Link coloring ON on input line 6045.
Package hyperref Info: Link coloring with OCG OFF on input line 6052.
Package hyperref Info: PDF/A mode OFF on input line 6057.
LaTeX Info: Redefining \ref on input line 6097.
LaTeX Info: Redefining \pageref on input line 6101.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count287
\c@Item=\count288
\c@Hfootnote=\count289
)
Package hyperref Info: Driver (autodetected): hxetex.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2022-02-21 v7.00n Hyperref driver for XeTeX
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box55
\c@Hy@AnnotLevel=\count290
\HyField@AnnotCount=\count291
\Fld@listcount=\count292
\c@bookmark@seq@number=\count293
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)
\Hy@SectionHShift=\skip57
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2022/02/07 v1.28a mathematical typesetting tools
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count294
\calc@Bcount=\count295
\calc@Adimen=\dimen172
\calc@Bdimen=\dimen173
\calc@Askip=\skip58
\calc@Bskip=\skip59
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count296
\calc@Cskip=\skip60
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count297
\l_MT_multwidth_dim=\dimen174
\origjot=\skip61
\l_MT_shortvdotswithinadjustabove_dim=\dimen175
\l_MT_shortvdotswithinadjustbelow_dim=\dimen176
\l_MT_above_intertext_sep=\dimen177
\l_MT_below_intertext_sep=\dimen178
\l_MT_above_shortintertext_sep=\dimen179
\l_MT_below_shortintertext_sep=\dimen180
\xmathstrut@box=\box56
\xmathstrut@dim=\dimen181
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/bbm-macros/bbm.sty
Package: bbm 1999/03/15 V 1.2 provides fonts for set symbols - TH
LaTeX Font Info:    Overwriting math alphabet `\mathbbm' in version `bold'
(Font)                  U/bbm/m/n --> U/bbm/bx/n on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathbbmss' in version `bold'
(Font)                  U/bbmss/m/n --> U/bbmss/bx/n on input line 35.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip62
\bibsep=\skip63
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count298
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count299
\float@exts=\toks35
\float@box=\box57
\@float@everytoks=\toks36
\@floatcapt=\box58
)
\@float@every@algorithm=\toks37
\c@algorithm=\count300
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count301
\c@ALG@rem=\count302
\c@ALG@nested=\count303
\ALG@tlm=\skip64
\ALG@thistlm=\skip65
\c@ALG@Lnr=\count304
\c@ALG@blocknr=\count305
\c@ALG@storecount=\count306
\c@ALG@tmpcounter=\count307
\ALG@tmplength=\skip66
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/wrapfig/wrapfig.sty
\wrapoverhang=\dimen182
\WF@size=\dimen183
\c@WF@wrappedlines=\count308
\WF@box=\box59
\WF@everypar=\toks38
Package: wrapfig 2003/01/31  v 3.6
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip67
\enit@outerparindent=\dimen184
\enit@toks=\toks39
\enit@inbox=\box60
\enit@count@id=\count309
\enitdp@description=\count310
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2022-02-07 L3 backend support: XeTeX
 (|extractbb --version)
\c__kernel_sys_dvipdfmx_version_int=\count311
\l__color_backend_stack_int=\count312
\g__color_backend_stack_int=\count313
\g__graphics_track_int=\count314
\l__pdf_internal_box=\box61
\g__pdf_backend_object_int=\count315
\g__pdf_backend_annotation_int=\count316
\g__pdf_backend_link_int=\count317
) (./main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 33.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 33.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2019/12/16 v2.5j Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

\c@lstlisting=\count318
Package hyperref Info: Link coloring ON on input line 33.
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count319
)
LaTeX Info: Redefining \ref on input line 33.
LaTeX Info: Redefining \pageref on input line 33.
LaTeX Info: Redefining \nameref on input line 33.
 (./main.out) (./main.out)
\@outlinefile=\write4
\openout4 = `main.out'.

 (./main.toc
LaTeX Font Info:    Trying to load font information for U+msa on input line 4.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 4.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+lasy on input line 4.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/ulasy.fd
File: ulasy.fd 1998/08/17 v2.2e LaTeX symbol font definitions
)
LaTeX Font Info:    Font shape `U/lasy/b/n' in size <8> not available
(Font)              Font shape `U/lasy/m/n' tried instead on input line 4.
LaTeX Font Info:    Font shape `U/lasy/b/n' in size <6> not available
(Font)              Font shape `U/lasy/m/n' tried instead on input line 4.
)
\tf@toc=\write5
\openout5 = `main.toc'.

 [1

] [2] [3] [4] [5] (./main.bbl [6]) [7] (./main.aux)
Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: 90AE0464E5EECD4BB5A3363705DD0453;1751.
 ) 
Here is how much of TeX's memory you used:
 15840 strings out of 476179
 259177 string characters out of 5804251
 633207 words of memory out of 5000000
 36308 multiletter control sequences out of 15000+600000
 492191 words of font info for 69 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 110i,16n,106p,378b,461s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on main.pdf (7 pages).
