## Step 1 : Import packages

import numpy as np
import sympy as sp
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt
from matplotlib.colors import Normalize
from matplotlib.cm import ScalarMappable
from matplotlib import rcParams
config = {
  "font.family":'sans serif',
  "font.size": 12,
  "mathtext.fontset":'custom',
  "font.sans-serif": ['Arial'],
  "axes.unicode_minus":False,
}
rcParams.update(config)

# =============================================================================
# 1. 参数和模型定义 (根据论文 Sec. III)
# =============================================================================
# 仿真参数
N_SAMPLES = 5000  # 论文中的样本数
T_FINAL = 1.0     # 最终时间 (Vanderpol's)
NT = 7            # KL展开的项数
SIGMA_SQUARED = 2 * np.pi # 论文中指定的噪声自相关

# 定义要绘图的时间点
t_eval = np.array([0.0, 0.5, 0.75, 1.0])

# =============================================================================
# 2. 动力学和散度
# =============================================================================

def get_vdp_divergence_func():
  """使用Sympy为VDP的确定性部分计算散度"""
  x1_s, x2_s = sp.symbols('x1 x2')
  
  f1 = x2_s
  f2 = (1 - x1_s**2) * x2_s - x1_s
  
  F = sp.Matrix([f1, f2])
  divF = F[0].diff(x1_s) + F[1].diff(x2_s) # d(f1)/dx1 + d(f2)/dx2
  
  # 结果非常简单: divF = 0 + (1 - x1^2) = 1 - x1^2
  # print(f"VDP Divergence: {divF}") 
  
  divF_func = sp.lambdify([x1_s, x2_s], divF, 'numpy')
  return divF_func

vdp_divergence = get_vdp_divergence_func()


def klpf_dynamics(t, y_aug, T, Nt):
  """
  KLPF增广系统的动力学方程
  y_aug = [x1, x2, rho, zeta_1, ..., zeta_Nt]
  zeta的值在积分过程中是固定的，所以我们只需要演化前三项
  """
  x1, x2, rho = y_aug[:3]
  zetas = y_aug[3:] # KL 随机参数

  # --- 1. 计算KL展开项 (过程噪声) ---
  kl_term = 0.0
  # i 从 1 到 Nt
  i_vals = np.arange(1, Nt + 1)
  # 使用向量化计算来加速
  kl_term = np.sqrt(2) * np.sum(zetas * np.cos((2 * i_vals - 1) * np.pi * t / (2 * T)))

  # --- 2. 计算状态的导数 ---
  dx1_dt = x2
  dx2_dt = (1 - x1**2) * x2 - x1 + kl_term
  
  # --- 3. 计算概率密度的导数 ---
  # 散度只与确定性部分 f 相关
  divF = vdp_divergence(x1, x2) 
  drho_dt = -rho * divF
  
  # --- 4. KL参数的导数为0 ---
  dzetas_dt = np.zeros_like(zetas)

  # 组合所有导数
  d_y_aug_dt = np.hstack(([dx1_dt, dx2_dt, drho_dt], dzetas_dt))
  
  return d_y_aug_dt

# =============================================================================
# 3. 初始样本生成
# =============================================================================
# 设置随机种子以保证结果可复现
np.random.seed(42)

# --- 初始状态 x0 ---
# 论文中指定初始PDF为 N([0,0]^T, diag(1,1))
mean_x0 = [0, 0]
cov_x0 = np.diag([1, 1])
initial_states = np.random.multivariate_normal(mean_x0, cov_x0, N_SAMPLES)

# --- 初始KL参数 zetas ---
# 论文中 dw(t) = xi(t)dt, E[xi(t)xi(tau)] = Q * delta(t-tau)
# Corrolary 2, E[zeta_i^2] = sigma^2
# 这里论文写的是 autocorrelation 2*pi*I，对于白噪声，这对应于Q=2*pi
# 所以 E[zeta_i^2] = 2*pi
std_zeta = np.sqrt(SIGMA_SQUARED)
initial_zetas = np.random.normal(0, std_zeta, size=(N_SAMPLES, NT))

# --- 初始概率密度 rho0 ---
# p0(z) = p(x0) * p(zetas)
# 由于我们只关心p(x(t))的相对大小，可以只用 p(x0)
# p(x0) 是一个二维高斯分布
from scipy.stats import multivariate_normal
p_x0_func = multivariate_normal(mean=mean_x0, cov=cov_x0)
initial_rho = p_x0_func.pdf(initial_states)

# --- 组合成增广初始状态 ---
# y_aug = [x1, x2, rho, zeta_1, ..., zeta_Nt]
initial_aug_states = np.hstack([initial_states, initial_rho.reshape(-1, 1), initial_zetas])

# =============================================================================
# 4. 运行仿真
# =============================================================================
# 存储结果
# 字典的键是时间点, 值是 N_SAMPLES x (2+1) 的数组 [x1, x2, rho]
results = {t: [] for t in t_eval}

t_span = [0, T_FINAL]

print(f"开始进行 {N_SAMPLES} 次仿真 (KL展开项数 Nt={NT})...")

# 对每一个样本进行积分
for i, y0_aug in enumerate(initial_aug_states):
  sol = solve_ivp(
    fun=klpf_dynamics,
    t_span=t_span,
    y0=y0_aug,
    t_eval=t_eval,
    method='RK45',
    args=(T_FINAL, NT) # 将T和Nt作为额外参数传入动力学函数
  )
  
  # 提取每个时间点的结果 (只需要x1, x2, rho)
  for t_idx, t in enumerate(t_eval):
    # sol.y 是 (state_dim, time_points)
    # 我们需要第 t_idx 列的前3个元素
    results[t].append(sol.y[:3, t_idx])

  if (i + 1) % 500 == 0:
    print(f"  已完成 {i+1}/{N_SAMPLES} 次仿真...")

# 转换结果为Numpy数组
for t in t_eval:
  results[t] = np.array(results[t])

# =============================================================================
# 5. 绘图 (复现 Fig.1 Top Row)
# =============================================================================
print("正在生成图像...")

fig, axes = plt.subplots(1, 4, figsize=(20, 5))
fig.suptitle("KLPF Uncertainty Propagation for Van der Pol's Oscillator (Fig.1 Top Row)", fontsize=16)

# 创建一个全局的颜色映射范围，以保证所有子图的颜色尺度一致
# 我们使用所有时间点中rho的最大和最小值来确定范围
all_rhos = np.concatenate([results[t][:, 2] for t in t_eval])
# 使用百分位数来避免极端值影响颜色条
vmin = np.percentile(all_rhos, 5)
vmax = np.percentile(all_rhos, 95)
norm = Normalize(vmin=vmin, vmax=vmax)
cmap = plt.get_cmap('jet') # 论文中使用了类似的 'jet' 或 'rainbow' 色彩

for ax, t in zip(axes, t_eval):
  data = results[t]
  x1_vals = data[:, 0]
  x2_vals = data[:, 1]
  rho_vals = data[:, 2]
  
  # 绘制散点图，颜色由rho值决定
  sc = ax.scatter(x1_vals, x2_vals, c=rho_vals, cmap=cmap, norm=norm, s=5, alpha=0.7)
  
  # --- 美化图像 ---
  ax.set_title(f"time = {t}s")
  ax.set_xlabel("$x_1$")
  ax.set_ylabel("$x_2$")
  ax.set_xlim(-3, 3)
  ax.set_ylim(-4, 4)
  ax.grid(True, linestyle='--', alpha=0.5)
  ax.set_aspect('equal', adjustable='box')


# 在图像右侧添加一个颜色条
fig.colorbar(ScalarMappable(norm=norm, cmap=cmap), ax=axes.ravel().tolist(), orientation='vertical', label='Probability Density (ρ)')

# plt.tight_layout(rect=[0, 0, 0.95, 0.95]) # 调整布局为颜色条留出空间
plt.show()

# =============================================================================
# 5. 绘图 (复现 Fig.1 Top Row 的3D形式)
# =============================================================================
print("正在生成3D散点图...")

fig = plt.figure(figsize=(18, 4))

# 创建一个全局的颜色映射范围，以保证所有子图的颜色尺度一致
all_rhos = np.concatenate([results[t][:, 2] for t in t_eval])
# 使用百分位数来避免极端值影响颜色条
vmin = np.percentile(all_rhos, 5)
vmax = np.percentile(all_rhos, 95)
norm = Normalize(vmin=vmin, vmax=vmax)
cmap = plt.get_cmap('jet') # 论文中使用了类似的 'jet' 或 'rainbow' 色彩

# 遍历每个时间点并创建3D子图
for i, t in enumerate(t_eval):
  ax = fig.add_subplot(1, 4, i + 1, projection='3d')
  
  data = results[t]
  x1_vals = data[:, 0]
  x2_vals = data[:, 1]
  rho_vals = data[:, 2]
  
  # 绘制3D散点图
  # x, y, z 分别是 x1, x2, rho
  # c (颜色) 也由 rho 决定
  sc = ax.scatter(
      x1_vals, x2_vals, rho_vals,
      c=rho_vals,      # 颜色值
      cmap=cmap,       # 颜色映射
      norm=norm,       # 颜色标准化范围
      s=5,             # 点的大小
      alpha=0.6,       # 点的透明度
      depthshade=True  # 根据深度调整亮度，增加3D感
  )

  # --- 美化图像 ---
  ax.set_title(f"time = {t}s", fontsize=14)
  ax.set_xlabel("$x_1$", fontsize=12, labelpad=10)
  ax.set_ylabel("$x_2$", fontsize=12, labelpad=10)
  ax.set_zlabel(r"Density ($p$)", fontsize=12, labelpad=10)
  ax.set_xlim([-5, 5])
  ax.set_ylim([-5, 5])
  
  # 调整视角
  ax.view_init(elev=25, azim=-60) # 调整仰角和方位角以获得更好的视图

# 在图像右侧添加一个颜色条
fig.colorbar(ScalarMappable(norm=norm, cmap=cmap), ax=axes.ravel().tolist(), shrink=0.7, aspect=20, label='Probability Density (ρ)')

# plt.tight_layout() # 为颜色条和标题调整布局
plt.show()